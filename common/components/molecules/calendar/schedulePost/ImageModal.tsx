'use client'

import {
  useState, useEffect,
} from 'react';
import { CanvasEditor } from '@/common/components/organisms';
import { ImageStyles } from '@/common/constants';
import toast from 'react-hot-toast';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  agentId: string;
  planId: string;
  content?: string;
  onImageAttached: (imageUrl: string, isFromAI: boolean, file?: File) => void;
}

export const ImageModal = ({
  isOpen,
  onClose,
  agentId,
  planId,
  content = '',
  onImageAttached,
}: ImageModalProps) => {
  const [imagePrompt, setImagePrompt] = useState(content.trim() || '');
  const [selectedOption, setSelectedOption] = useState(ImageStyles[0]);
  const [currentImage, setCurrentImage] = useState('');
  const [imagePromptError, setImagePromptError] = useState('');
  const [imageGenerating, setImageGenerating] = useState(false);
  const [isUploadFromLocal, setIsUploadFromLocal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [activeTab, setActiveTab] = useState('generate');
  const [isCanvasEditorOpen, setIsCanvasEditorOpen] = useState(false);
  const { trackContentEvent } = useMixpanelEvent();

  useEffect(() => {
    setImagePrompt(content.trim() || '');
  }, [content, isOpen]);


  // const handleGenerate = async () => {
  //   if (!imagePrompt) {
  //     setImagePromptError('Please enter an image prompt');
  //     return;
  //   }
  //   if (imagePrompt.length < 10) {
  //     setImagePromptError('Image prompt should be at least 10 characters');
  //     return;
  //   }
  //   if (imagePrompt.length > 400) {
  //     setImagePromptError('Image prompt should not exceed 400 characters');
  //     return;
  //   }
  //   setImagePromptError('');
  //   setImageGenerating(true);

  //   try {
  //     if (!agentId) {
  //       throw new Error('Agent ID is required for image generation');
  //     }

  //     const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
  //     const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

  //     const response = await fetch(endpoint, {
  //       method: 'POST',
  //       body: JSON.stringify({
  //         description: imagePrompt,
  //         style: selectedOption?.option,
  //         planId: planId || 'new-post',
  //       }),
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //     });

  //     if (!response.ok) {
  //       throw new Error(`Failed to generate image: ${response.statusText}`);
  //     }

  //     const imageData = await response.json();

  //     if (imageData && imageData.filepath) {
  //       setCurrentImage(getPath(imageData.filepath))
  //       setIsUploadFromLocal(false);

  //       trackContentEvent('image', {
  //         prompt: imagePrompt,
  //         imageStyle: selectedOption?.option,
  //       });
  //     } else {
  //       throw new Error('Invalid response from image generation API');
  //     }
  //   } catch (error) {
  //     console.error('Error generating image:', error);
  //     toast.error('Failed to generate image');
  //   } finally {
  //     setImageGenerating(false);
  //   }
  // };

  // const handleAttachImage = () => {
  //   if (isUploadFromLocal) {
  //     if (selectedFile) {
  //       onImageAttached(currentImage, false, selectedFile);
  //     }
  //   } else {
  //     onImageAttached(currentImage, true);
  //   }
  //   onClose();
  // };

  const handleCanvasEditorSave = (imageUrl: string) => {
    setCurrentImage(imageUrl);
    setIsUploadFromLocal(false);
    setIsCanvasEditorOpen(false);
    toast.success('Media saved!');
  };

  // const GenerateImagePanel = () => {

  //   return (
  //     <div className="p-6">
  //       <div className="mb-6">
  //         <h3 className="text-white font-semibold text-lg mb-2">Generate Image</h3>
  //         <p className="text-gray-400 text-sm">Create images using AI</p>
  //       </div>
  //       <div className="space-y-4">
  //         <div>
  //           <label className="text-white text-sm font-medium mb-2 block">Description</label>
  //           <textarea
  //             value={imagePrompt}
  //             onChange={(e) => setImagePrompt(e.target.value)}
  //             placeholder="Describe the image you want to generate..."
  //             className="w-full h-24 p-3 bg-neutral-800 text-white rounded-lg border border-neutral-600 resize-none focus:border-violets-are-blue focus:outline-none transition-colors"
  //             disabled={imageGenerating}
  //           />
  //           <div className="text-xs text-gray-400 mt-1">
  //             {imagePrompt.length}/400 characters
  //           </div>
  //         </div>

  //         <div>
  //           <label className="text-white text-sm font-medium mb-2 block">Style</label>
  //           <select
  //             value={selectedOption?.option || ''}
  //             onChange={(e) => {
  //               const style = ImageStyles.find(s => s.option === e.target.value);
  //               if (style) {
  //                 setSelectedOption(style);
  //               }
  //             }}
  //             className="w-full bg-neutral-800 text-white border border-neutral-600 rounded-lg p-2 focus:border-violets-are-blue focus:outline-none"
  //             disabled={imageGenerating}
  //           >
  //             {ImageStyles.map((style) => (
  //               <option key={style.option} value={style.option}>
  //                 {style.label}
  //               </option>
  //             ))}
  //           </select>
  //         </div>

  //         {imagePromptError && (
  //           <div className="text-red-400 text-sm bg-red-400/10 p-2 rounded-lg">
  //             {imagePromptError}
  //           </div>
  //         )}

  //         <button
  //           onClick={handleGenerate}
  //           disabled={imageGenerating || !imagePrompt.trim()}
  //           className="w-full bg-gradient-to-r from-violets-are-blue to-han-purple hover:from-violets-are-blue/90 hover:to-han-purple/90 text-white py-3 px-4 rounded-lg transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
  //         >
  //           {imageGenerating ? 'Generating...' : 'Generate Image'}
  //         </button>
  //       </div>
  //     </div>
  //   );
  // };

  // // Render active tab content
  // const renderTabContent = () => {
  //   switch (activeTab) {
  //   case 'generate':
  //     return <GenerateImagePanel />;
  //   case 'upload':
  //     return (
  //       <div className="p-6">
  //         <div className="mb-6">
  //           <h3 className="text-white font-semibold text-lg mb-2">Upload Image</h3>
  //           <p className="text-gray-400 text-sm">Add images from your device</p>
  //         </div>
  //         <PhotoUploader
  //           id='image-upload'
  //           setValue={setCurrentImage}
  //           setSelectedFile={setSelectedFile}
  //           setIsUploadFromLocal={setIsUploadFromLocal}
  //           loading={imageGenerating}
  //           value={currentImage}
  //           errorMessage=''
  //         />
  //       </div>
  //     );
  //   case 'canvas':
  //     return (
  //       <div className="p-6">
  //         <div className="mb-6">
  //           <h3 className="text-white font-semibold text-lg mb-2">Canvas Editor</h3>
  //           <p className="text-gray-400 text-sm">Create designs with text, images, and shapes</p>
  //         </div>
  //         <div className="space-y-4">
  //           <button
  //             onClick={() => setIsCanvasEditorOpen(true)}
  //             className="w-full bg-gradient-to-r from-violets-are-blue to-han-purple hover:from-violets-are-blue/90 hover:to-han-purple/90 text-white py-3 px-4 rounded-lg transition-all duration-200 font-medium shadow-lg flex items-center justify-center gap-2"
  //           >
  //             <Palette size={18} />
  //             Open Canvas Editor
  //           </button>
  //           <div className="text-xs text-gray-500 bg-neutral-800 p-3 rounded-lg">
  //             <p className="mb-1">🎨 <strong>Canvas Editor Features:</strong></p>
  //             <p>• Add and customize text with fonts and colors</p>
  //             <p>• Upload and position images</p>
  //             <p>• Add shapes, lines, and elements</p>
  //             <p>• Generate AI images directly on canvas</p>
  //             <p>• Full editing tools with undo/redo</p>
  //           </div>
  //         </div>
  //       </div>
  //     );
  //   case 'recent':
  //     return (
  //       <div className="p-6">
  //         <div className="mb-6">
  //           <h3 className="text-white font-semibold text-lg mb-2">Recent Uploads</h3>
  //           <p className="text-gray-400 text-sm">Your recently uploaded images</p>
  //         </div>
  //         <div className="text-gray-400 text-sm text-center py-12">
  //           <ImageIcon className="mx-auto mb-3 text-gray-500" size={32} />
  //           <p>No recent uploads</p>
  //           <p className="text-xs mt-1">Upload images to see them here</p>
  //         </div>
  //       </div>
  //     );
  //   default:
  //     return <GenerateImagePanel />;
  //   }
  // };

  return (
    <CanvasEditor
      isOpen={isOpen}
      onClose={() => onClose()}
      onSave={handleCanvasEditorSave}
      initialImage={currentImage || undefined}
      agentId={agentId}
      planId={planId}
    />
  );
};

export default ImageModal;
