'use client'

import React, { 
  useCallback,
  useEffect, useRef, useState,
} from 'react';
import * as fabric from 'fabric';
import {
  Wand2,
  Upload,
  Type,
  Clock,
  Image as ImageIcon,
  LucideProps,
} from 'lucide-react';
import autosize from 'autosize';
import { 
  acceptedImageMimeTypes, FILE_SIZE_10_MB, ImageStyles,
} from '@/common/constants';
import { getPath } from '@/common/utils/helpers';
import toast from 'react-hot-toast';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import { 
  Button, TextArea,
} from '../../atoms';
import { 
  Dropdown, DropdownOption,
} from '../../molecules';

interface CanvasSidebarProps {
  canvas: fabric.Canvas | null;
  agentId: string;
  planId: string;
}

interface SidebarTab {
  id: string;
  icon: React.ForwardRefExoticComponent<Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>>;
  label: string;
  content: React.ComponentType<{
    canvas: fabric.Canvas | null;
    agentId?: string;
    planId?: string;
  }>;
}

const GenerateImagePanel = ({
  canvas,
  agentId,
  planId,
}: {
  canvas: fabric.Canvas | null;
  agentId?: string;
  planId?: string;
}) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState(ImageStyles[0]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const { trackContentEvent } = useMixpanelEvent();

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (option: DropdownOption) => {
    setSelectedStyle(option as typeof selectedStyle);
  };

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setIsGenerating(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify({
          description: imagePrompt,
          style: selectedStyle.option,
          planId: planId || 'new-post',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to generate image: ${response.statusText}`);
      }

      const imageData = await response.json();

      if (imageData && imageData.filepath && canvas) {
        const imageUrl = getPath(imageData.filepath);

        fabric.FabricImage.fromURL(imageUrl).then((img: any) => {
          img.scaleToWidth(300);
          img.set({
            left: (canvas.width! - img.getScaledWidth()) / 2,
            top: (canvas.height! - img.getScaledHeight()) / 2,
          });
          canvas.add(img);
          canvas.renderAll();
        });

        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle.option,
        });

        toast.success('Image generated and added to canvas!');
        setImagePrompt('');
      } else {
        throw new Error('Invalid response from image generation API');
      }
    } catch (error) {
      console.error('Error generating image:', error);
      toast.error('Failed to generate image');
      setError('Failed to generate image. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="px-6 py-4">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg">Generate Image</h3>
        <p className="text-gray-400 text-sm">Create and add images using AI</p>
      </div>
      <div className="space-y-4">
        <div>
          <label htmlFor="image-prompt" className="text-white font-medium text-sm">
            Description
            <TextArea
              ref={imagePromptRef}
              name="image-prompt"
              id="image-prompt"
              width='w-full'
              maxHeight='80px'
              disabled={isGenerating}
              placeholder='Describe the image you want to generate...'
              value={imagePrompt}
              onChange={(e) => setImagePrompt(e.target.value)}
            />
          </label>
        </div>

        <Dropdown
          label="Image Style"
          options={ImageStyles}
          selectedOption={selectedStyle}
          onSelect={handleStyleSelect}
          placeholder="Select a style"
        />

        {error && (
          <div className="text-tulip text-sm">
            {error}
          </div>
        )}
        <div className='!mt-6'>
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !imagePrompt.trim()}
            variant='gradient'
            size='md'
            width='w-full'
          >
            {isGenerating ? 'Generating...' : 'Generate Image'}
          </Button>
        </div>
      </div>
    </div>
  );
};

const UploadImagePanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {

    if (!acceptedImageMimeTypes.includes(file.type)) {
      return 'Please upload a valid image file (JPEG, PNG, GIF, SVG, or WebP)';
    }

    if (file.size > FILE_SIZE_10_MB) {
      return 'File size must be less than 10MB';
    }

    return null;
  };

  const handleFileUpload = async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      return;
    }

    setIsUploading(true);

    try {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imgUrl = event.target?.result as string;
        if (canvas) {
          fabric.FabricImage.fromURL(imgUrl).then((img: any) => {
            const maxWidth = 400;
            const maxHeight = 400;

            if (img.width! > maxWidth || img.height! > maxHeight) {
              const scaleX = maxWidth / img.width!;
              const scaleY = maxHeight / img.height!;
              const scale = Math.min(scaleX, scaleY);
              img.scale(scale);
            }

            img.set({
              left: (canvas.width! - img.getScaledWidth()) / 2,
              top: (canvas.height! - img.getScaledHeight()) / 2,
            });
            canvas.add(img);
            canvas.renderAll();

            addToRecentUploads(imgUrl, file.name);

            toast.success('Image added to canvas!');
          });
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="px-6 py-4">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg">Upload Image</h3>
        <p className="text-gray-400 text-sm">Add images from your device</p>
      </div>
      <div className="space-y-4">
        <div
          onClick={handleClick}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          className={`border-2 border-dashed rounded-xl p-8 text-center transition-all cursor-pointer ${
            isDragging
              ? 'border-violets-are-blue bg-violets-are-blue/10'
              : 'border-neutral-600 hover:border-violets-are-blue'
          } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <ImageIcon className="mx-auto mb-3 text-gray-400" size={40} />
          <p className="text-gray-400 text-sm mb-2">
            {isDragging ? 'Drop image here' : 'Drag & drop or click to upload'}
          </p>
          <p className="text-gray-500 text-xs">PNG, JPG, SVG, GIF, WebP up to 10MB</p>
          {isUploading && (
            <div className="mt-3">
              <div className="text-violets-are-blue text-sm">Uploading...</div>
            </div>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleFileInputChange}
          disabled={isUploading}
        />

        <div className="text-xs text-gray-500">
          <p>• Supported formats: JPEG, PNG, GIF, WebP</p>
          <p>• Maximum file size: 10MB</p>
        </div>
      </div>
    </div>
  );
};

const TextPanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => {
  const [fontFamily, setFontFamily] = useState('Arial');
  const [fontSize, setFontSize] = useState(24);
  const [textColor, setTextColor] = useState('#000000');
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);

  const fonts = [
    'Arial',
    'Helvetica',
    'Times New Roman',
    'Georgia',
    'Verdana',
    'Comic Sans MS',
    'Impact',
    'Trebuchet MS',
    'Courier New',
    'Palatino',
  ];

  const addText = () => {
    if (!canvas) {
      return;
    }
    const text = new fabric.Text('Click to edit', {
      left: 100,
      top: 100,
      fontFamily: fontFamily,
      fontSize: fontSize,
      fill: textColor,
      fontWeight: isBold ? 'bold' : 'normal',
      fontStyle: isItalic ? 'italic' : 'normal',
    });

    canvas.add(text);
    canvas.setActiveObject(text);
    canvas.renderAll();
    toast.success('Text added to canvas!');
  };

  const updateSelectedText = useCallback(() => {
    if (!canvas) {
      return;
    }
    const activeObject = canvas.getActiveObject();
    if (activeObject && activeObject.type === 'text') {
      const textObject = activeObject as fabric.Text;
      textObject.set({
        fontFamily: fontFamily,
        fontSize: fontSize,
        fill: textColor,
        fontWeight: isBold ? 'bold' : 'normal',
        fontStyle: isItalic ? 'italic' : 'normal',
      });
      canvas.renderAll();
    }
  }, [canvas, fontFamily, fontSize, textColor, isBold, isItalic]);

  React.useEffect(() => {
    updateSelectedText();
  }, [fontFamily, fontSize, textColor, isBold, isItalic, updateSelectedText]);

  return (
    <div className="px-6 py-4">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg">Text Tools</h3>
        <p className="text-gray-400 text-sm">Add and customize text</p>
      </div>
      <div className="space-y-6">
        <Button
          variant="gradient"
          size="md"
          width="w-full"
          onClick={addText}
        >
          Add Text
        </Button>

        <div className="space-y-4">
          <div>
            {/* <label className="text-white text-sm font-medium mb-2 block">Font Family</label>
            <select
              value={fontFamily}
              onChange={(e) => setFontFamily(e.target.value)}
              className="w-full bg-neutral-800 text-white border border-neutral-600 rounded-lg p-2 focus:border-violets-are-blue focus:outline-none"
            >
              {fonts.map((font) => (
                <option key={font} value={font} style={{ fontFamily: font }}>
                  {font}
                </option>
              ))}
            </select> */}
            <Dropdown
              label="Font Family"
              options={fonts.map(font => ({ 
                value: font, 
                label: font,
              }))}
              selectedOption={{ 
                label: fontFamily, 
                option: fontFamily,
              }}
              onSelect={(option) => {
                console.
                setFontFamily(option.option)
              }}
              placeholder="Select Font Style"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Font Size: {fontSize}px
            </label>
            <input
              type="range"
              min="12"
              max="120"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
              className="w-full accent-violets-are-blue"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Color</label>
            <input
              type="color"
              value={textColor}
              onChange={(e) => setTextColor(e.target.value)}
              className="w-full h-10 rounded-lg p-0.5 py-0 border border-neutral-600"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Text Style</label>
            <div className="flex gap-2">
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setIsBold(!isBold)}
                className={`${isBold ? "text-violets-are-blue !border-violets-are-blue" : ""}`}
              >
                Bold
              </Button>
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setIsItalic(!isItalic)}
                className={`${isItalic ? "text-violets-are-blue !border-violets-are-blue" : ""}`}
              >
                Italic
              </Button>
            </div>
          </div>
        </div>

        <div className="text-xs text-gray-500 bg-neutral-800 p-3 rounded-xl">
          <p className="mb-1">💡 <strong>Tips:</strong></p>
          <p>• Select text on canvas to modify</p>
          <p>• Double-click text to edit content</p>
          <p>• Use Delete key to remove selected text</p>
        </div>
      </div>
    </div>
  );
};

// Simple storage for recent uploads (in a real app, this would be in a global state or database)
const getRecentUploads = (): Array<{id: string, url: string, name: string, timestamp: number}> => {
  if (typeof window === 'undefined') return [];
  const stored = localStorage.getItem('canvas-recent-uploads');
  return stored ? JSON.parse(stored) : [];
};

const addToRecentUploads = (url: string, name: string) => {
  if (typeof window === 'undefined') return;
  const recent = getRecentUploads();
  const newUpload = {
    id: Date.now().toString(),
    url,
    name,
    timestamp: Date.now()
  };

  // Add to beginning and keep only last 20
  const updated = [newUpload, ...recent.filter(item => item.url !== url)].slice(0, 20);
  localStorage.setItem('canvas-recent-uploads', JSON.stringify(updated));
};

const RecentUploadsPanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => {
  const [recentUploads, setRecentUploads] = useState<Array<{id: string, url: string, name: string, timestamp: number}>>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load recent uploads on component mount
  React.useEffect(() => {
    setRecentUploads(getRecentUploads());
  }, []);

  const addImageToCanvas = (imageUrl: string, imageName: string) => {
    if (!canvas) return;

    setIsLoading(true);
    fabric.FabricImage.fromURL(imageUrl, { crossOrigin: 'anonymous' }).then((img: any) => {
      // Scale image to fit nicely on canvas
      const maxWidth = 400;
      const maxHeight = 400;

      if (img.width! > maxWidth || img.height! > maxHeight) {
        const scaleX = maxWidth / img.width!;
        const scaleY = maxHeight / img.height!;
        const scale = Math.min(scaleX, scaleY);
        img.scale(scale);
      }

      // Manually center the image
      img.set({
        left: (canvas.width! - img.getScaledWidth()) / 2,
        top: (canvas.height! - img.getScaledHeight()) / 2
      });
      canvas.add(img);
      canvas.setActiveObject(img);
      canvas.renderAll();
      toast.success(`${imageName} added to canvas!`);
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
      toast.error('Failed to load image');
    });
  };

  const clearRecentUploads = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('canvas-recent-uploads');
      setRecentUploads([]);
      toast.success('Recent uploads cleared!');
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-white font-semibold text-lg">Recent Uploads</h3>
          {recentUploads.length > 0 && (
            <button
              onClick={clearRecentUploads}
              className="text-xs text-gray-400 hover:text-red-400 transition-colors"
            >
              Clear all
            </button>
          )}
        </div>
        <p className="text-gray-400 text-sm">Your recently uploaded images</p>
      </div>

      {recentUploads.length === 0 ? (
        <div className="text-gray-400 text-sm text-center py-12">
          <ImageIcon className="mx-auto mb-3 text-gray-500" size={32} />
          <p>No recent uploads</p>
          <p className="text-xs mt-1">Upload images to see them here</p>
        </div>
      ) : (
        <div className="space-y-3">
          {recentUploads.map((upload) => (
            <div
              key={upload.id}
              className="bg-neutral-800 rounded-lg p-3 border border-neutral-700 hover:border-violets-are-blue transition-colors cursor-pointer group"
              onClick={() => addImageToCanvas(upload.url, upload.name)}
            >
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-neutral-700 rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={upload.url}
                    alt={upload.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-white text-sm font-medium truncate group-hover:text-violets-are-blue transition-colors">
                    {upload.name}
                  </p>
                  <p className="text-gray-400 text-xs">
                    {formatDate(upload.timestamp)}
                  </p>
                </div>
                <div className="text-gray-400 group-hover:text-violets-are-blue transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {isLoading && (
        <div className="text-center py-4">
          <div className="text-violets-are-blue text-sm">Adding image to canvas...</div>
        </div>
      )}

      <div className="text-xs text-gray-500 bg-neutral-800 p-3 rounded-lg mt-6">
        <p className="mb-1">💡 <strong>Tips:</strong></p>
        <p>• Click any image to add it to the canvas</p>
        <p>• Recent uploads are stored locally</p>
        <p>• Only the last 20 uploads are kept</p>
      </div>
    </div>
  );
};

export const CanvasSidebar = ({ 
  canvas, agentId, planId,
}: CanvasSidebarProps) => {
  const [activeTab, setActiveTab] = useState('generate');

  const tabs: SidebarTab[] = [
    {
      id: "generate",
      icon: Wand2,
      label: "Generate",
      content: GenerateImagePanel,
    },
    { 
      id: "upload", 
      icon: Upload, 
      label: "Upload", 
      content: UploadImagePanel,
    },
    { 
      id: "text", 
      icon: Type,
      label: "Text", 
      content: TextPanel,
    },
    { 
      id: "recent", 
      icon: Clock, 
      label: "Recent", 
      content: RecentUploadsPanel,
    },
  ];

  const ActiveContent = tabs.find(tab => tab.id === activeTab)?.content || GenerateImagePanel;

  return (
    <div className="w-96 bg-neutral-900 border-r border-neutral-700 flex h-full">
      <div className="w-20 bg-neutral-800 flex flex-col border-r border-neutral-700">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`p-4 flex flex-col items-center gap-1 transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-tr from-violets-are-blue to-han-purple text-white shadow-lg'
                  : 'text-gray-400 hover:text-white hover:bg-neutral-700'
              }`}
              title={tab.label}
            >
              <Icon size={18} />
              <span className="text-xs font-medium">{tab.label}</span>
            </button>
          );
        })}
      </div>

      <div className="flex-1 overflow-y-auto bg-neutral-900">
        <div className="h-full">
          <ActiveContent canvas={canvas} agentId={agentId} planId={planId} />
        </div>
      </div>
    </div>
  );
};
