'use client'

import React from 'react';
import { Button } from '@/common/components/atoms';

interface CanvasToolbarProps {
  onClose: () => void;
  onSaveDesign: () => void;
}

export const CanvasToolbar = ({
  onClose,
  onSaveDesign,
}: CanvasToolbarProps) => {

  return (
    <div className="bg-eerie-black border-b border-neutral-700 px-2 md:px-4 py-2 md:py-3 flex items-center justify-between relative">
      <div className="flex items-center gap-1 md:gap-2">
      </div>
      <div className="absolute  left-1/2 transform -translate-x-1/2 text-center">
        <h1 className="text-white font-semibold text-sm md:text-lg">Image Editor</h1>
      </div>
      <div className="flex items-center gap-1 md:gap-2">
        <Button
          variant="gradient"
          size="sm"
          onClick={onSaveDesign}
        >
          Save
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
        >
          Close
        </Button>
      </div>
    </div>
  );
};
